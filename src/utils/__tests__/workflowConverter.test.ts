import { convertWorkflowToReactFlow, convertReactFlowToWorkflow } from '../workflowConverter';
import type { Workflow } from '../../stores/workflow_store';
import type { NodeType } from '../../service/nodeService';

// Mock node types for testing
const mockNodeTypes: NodeType[] = [
  {
    name: 'manual_trigger',
    display_name: 'Manual Trigger',
    description: 'Manually trigger the workflow',
    icon: 'play',
    icon_color: '#4CAF50',
    group: ['trigger'],
    version: 1,
    parameters: [],
    inputs: [],
    outputs: ['main'],
    hidden: false,
  },
  {
    name: 'wait',
    display_name: 'Wait',
    description: 'Wait for a specified amount of time',
    icon: 'clock',
    icon_color: '#FF9800',
    group: ['utility'],
    version: 1,
    parameters: [
      {
        name: 'wait_value',
        type: 'number',
        display_name: 'Wait Time',
        description: 'Time to wait',
        required: true,
        default: 1,
      },
      {
        name: 'wait_unit',
        type: 'options',
        display_name: 'Unit',
        description: 'Time unit',
        required: true,
        default: 'seconds',
        options: [
          { name: 'Seconds', value: 'seconds' },
          { name: 'Minutes', value: 'minutes' },
        ],
      },
    ],
    inputs: ['main'],
    outputs: ['main'],
    hidden: false,
  },
];

// Mock workflow data
const mockWorkflow: Workflow = {
  id: 'test-workflow-123',
  name: 'Test Workflow',
  active_version_id: 1,
  created_at: '2024-01-01T00:00:00Z',
  created_by: 1,
  is_active: true,
  tags: [],
  updated_at: '2024-01-01T00:00:00Z',
  version_no: 1,
  work_flow: {
    start_node: 'node-1',
    nodes: {
      'node-1': {
        name: 'manual_trigger',
        type: 'manual_trigger',
        display_name: 'Manual Trigger 1',
        description: 'Manually trigger the workflow',
        version: 1,
        position: [100, 100],
        parameters: {},
      },
      'node-2': {
        name: 'wait',
        type: 'wait',
        display_name: 'Wait 2',
        description: 'Wait for a specified amount of time',
        version: 1,
        position: [300, 200],
        parameters: {
          wait_value: 5,
          wait_unit: 'seconds',
        },
      },
    },
    connections: {
      'node-1': {
        main: [['node-2']],
      },
    },
  },
};

describe('workflowConverter', () => {
  describe('convertWorkflowToReactFlow', () => {
    it('should convert API workflow to ReactFlow format', () => {
      const result = convertWorkflowToReactFlow(mockWorkflow, mockNodeTypes);

      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
      expect(result.nodeCounter).toBe(3); // Next available node number

      // Check first node
      const node1 = result.nodes.find(n => n.id === 'node-1');
      expect(node1).toBeDefined();
      expect(node1?.type).toBe('workflowNode');
      expect(node1?.position).toEqual({ x: 100, y: 100 });
      expect(node1?.data.type).toBe('manual_trigger');
      expect(node1?.data.label).toBe('Manual Trigger 1');

      // Check second node
      const node2 = result.nodes.find(n => n.id === 'node-2');
      expect(node2).toBeDefined();
      expect(node2?.position).toEqual({ x: 300, y: 200 });
      expect(node2?.data.parameters).toEqual({
        wait_value: 5,
        wait_unit: 'seconds',
      });

      // Check edge
      const edge = result.edges[0];
      expect(edge.source).toBe('node-1');
      expect(edge.target).toBe('node-2');
      expect(edge.type).toBe('animatedEdge');
    });

    it('should handle workflows with no connections', () => {
      const workflowWithoutConnections: Workflow = {
        ...mockWorkflow,
        work_flow: {
          ...mockWorkflow.work_flow,
          connections: {},
        },
      };

      const result = convertWorkflowToReactFlow(workflowWithoutConnections, mockNodeTypes);

      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle empty workflow', () => {
      const emptyWorkflow: Workflow = {
        ...mockWorkflow,
        work_flow: {
          start_node: '',
          nodes: {},
          connections: {},
        },
      };

      const result = convertWorkflowToReactFlow(emptyWorkflow, mockNodeTypes);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
      expect(result.nodeCounter).toBe(1);
    });
  });

  describe('convertReactFlowToWorkflow', () => {
    it('should convert ReactFlow format back to API workflow format', () => {
      // First convert to ReactFlow format
      const reactFlowData = convertWorkflowToReactFlow(mockWorkflow, mockNodeTypes);
      
      // Then convert back to API format
      const result = convertReactFlowToWorkflow(reactFlowData.nodes, reactFlowData.edges);

      expect(Object.keys(result.nodes)).toHaveLength(2);
      expect(Object.keys(result.connections)).toHaveLength(1);

      // Check node structure
      const node1 = result.nodes['node-1'];
      expect(node1.type).toBe('manual_trigger');
      expect(node1.position).toEqual([100, 100]);
      expect(node1.is_trigger).toBe(true);

      const node2 = result.nodes['node-2'];
      expect(node2.type).toBe('wait');
      expect(node2.position).toEqual([300, 200]);
      expect(node2.parameters).toEqual({
        wait_value: 5,
        wait_unit: 'seconds',
      });

      // Check connections
      expect(result.connections['node-1'].main[0]).toContain('node-2');
    });
  });
});
